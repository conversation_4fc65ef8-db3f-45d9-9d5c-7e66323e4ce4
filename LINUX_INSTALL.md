# Infinity Notes Linux 版本使用说明

## 📦 安装包信息

### 当前版本：1.0.0

### 可用的 Linux 安装包：

1. **便携版 (推荐)：** `Infinity Notes-1.0.0-linux-x64.tar.gz` (114M)

   - ✅ 已生成
   - 无需安装，解压即用
   - 适合所有 Linux 发行版

2. **AppImage 版：** `Infinity Notes-1.0.0.AppImage`

   - ❌ 构建失败 (网络问题)
   - 单文件，双击运行

3. **DEB 包：** `infinity-notes-1.0.0.deb`
   - ❌ 构建失败 (网络问题)
   - 适合 Debian/Ubuntu 系统

## 🚀 使用便携版

### 1. 下载和解压

```bash
# 解压安装包
tar -xzf "Infinity Notes-1.0.0-linux-x64.tar.gz"

# 进入目录
cd linux-unpacked
```

### 2. 运行应用

```bash
# 直接运行
./infinity-notes

# 或者后台运行
nohup ./infinity-notes &
```

### 3. 创建桌面快捷方式 (可选)

```bash
# 创建桌面文件
cat > ~/.local/share/applications/infinity-notes.desktop << EOF
[Desktop Entry]
Name=Infinity Notes
Comment=无限画布思维整理工具
Exec=/path/to/linux-unpacked/infinity-notes
Icon=/path/to/linux-unpacked/resources/app.asar.unpacked/icon.png
Terminal=false
Type=Application
Categories=Office;Development;
EOF

# 使其可执行
chmod +x ~/.local/share/applications/infinity-notes.desktop
```

## 🔧 系统要求

- **操作系统：** Linux x64 (64 位)
- **最低要求：**
  - 内存：4GB RAM
  - 存储：500MB 可用空间
  - 显卡：支持硬件加速 (推荐)

## 📝 构建命令

如果你想自己构建 Linux 版本：

```bash
# 构建所有格式 (需要网络良好)
npm run dist:linux

# 仅构建便携版
npm run dist:linux:mirror
npm run package:linux

# 使用国内镜像构建
ELECTRON_MIRROR=https://npmmirror.com/mirrors/electron/ npm run dist:linux
```

## 🐛 常见问题

### Q: 运行时提示权限不足

```bash
chmod +x infinity-notes
./infinity-notes
```

### Q: 缺少依赖库

```bash
# Ubuntu/Debian
sudo apt install libgtk-3-0 libxss1 libasound2

# CentOS/RHEL
sudo yum install gtk3 libXScrnSaver alsa-lib

# Arch Linux
sudo pacman -S gtk3 libxss alsa-lib
```

### Q: 图标不显示

请确保系统支持 PNG 图标格式，或安装相应的图标主题。

## 📂 文件结构

```
linux-unpacked/
├── infinity-notes          # 主可执行文件
├── resources/
│   └── app.asar            # 应用资源
├── locales/                # 语言文件
└── lib*.so                 # 共享库文件
```

## 🔄 更新说明

要更新到新版本：

1. 备份你的数据 (通常在 `~/.config/infinity-notes/`)
2. 下载新版本的 tar.gz 包
3. 解压替换旧版本
4. 运行新版本

---

**版本：** 1.0.0  
**构建时间：** 2025-08-03  
**支持：** [GitHub Issues](https://github.com/duobaobox/infinity-notes/issues)
